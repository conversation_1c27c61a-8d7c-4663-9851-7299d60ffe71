<template>
  <view class="page">
    <view class="logo">
      <image src="../../static/logo.png" mode=""></image>
    </view>
    <!-- 填写区 -->
    <view class="other-ways">
      <text>下载安卓APP</text>
    </view>
    <!-- 登录方式 -->
    <view class="login-way">
      <view class="way" @click="gotoDown">
        <image :src="api+'/static/code.png'" mode="aspectFit"></image>
        <text>点击或扫码下载</text>

      </view>
      <view>
        <button @click="showQRCode">生成二维码</button>
        <!-- 添加canvas元素，注意设置宽高 -->
        <canvas
            canvas-id="qrcode-canvas"
            style="width: 200px; height: 200px;"
            v-if="showCanvas">
        </canvas>
        <!-- 显示生成的二维码图片 -->
        <image v-if="qrCodeUrl" :src="qrCodeUrl" mode="widthFix"></image>
      </view>
    </view>
    <view style="position: fixed;width: 100vw;height: 100vh;left: 0;top: 0;" v-if="showWX==1">
      <image :src="api+'/static/wxDown.jpg'" mode="scaleToFill" style="width: 100vw;height: 100vh;"></image>
    </view>
  </view>
</template>

<script>

import UQRCode from 'uqrcodejs';
export default {
  components: {},
  data() {
    return {
      api: getApp().globalData.apiUrl,
      showWX: 0,
      downloadUrl: '',
      qrCodeUrl: ''
    };
  },
  onLoad(option) {
    var ua = window.navigator.userAgent.toLowerCase();
    if (ua.match(/MicroMessenger/i) == 'micromessenger') {
      this.showWX = 1
    }
    this.getAppInfo();
  },
  methods: {
    gotoDown() {
      window.open(this.downloadUrl, "_self")
    },
    getAppInfo() {
      console.log('开始获取APP信息...');
      this.$http.get('getReleaseAppInfo', {
        os: 'android'
      }).then(res => {
        console.log('API响应：', res);
        if (res.code === 0 && res.data && res.data.version_info) {
          this.downloadUrl = res.data.version_info.url.trim();
          console.log('获取到下载链接：', this.downloadUrl);
        } else {
          console.log('API响应异常：', res);
          uni.showToast({
            title: '暂无可用版本',
            icon: 'none'
          });
        }
      }).catch(err => {
        console.error('API调用失败：', err);
        uni.showToast({
          title: '获取版本信息失败',
          icon: 'none'
        });
      });
    },
    // 生成二维码
    generateQRCode(text) {
      return new Promise((resolve) => {
        // 创建实例
        const qr = new UQRCode();

        // 设置参数
        qr.data = text;
        qr.size = 200;
        qr.margin = 10;
        qr.foreground = '#000000';
        qr.background = '#ffffff';
        qr.errorCorrectLevel = UQRCode.errorCorrectLevel.H;

        // 生成二维码
        qr.make();

        // 获取canvas上下文
        const canvas = uni.createCanvasContext('qrcode-canvas', this);

        // 绘制二维码到canvas
        qr.canvasContext = canvas;
        qr.drawCanvas();

        // 获取临时文件路径
        setTimeout(() => {
          uni.canvasToTempFilePath({
            canvasId: 'qrcode-canvas',
            success: res => {
              resolve(res.tempFilePath);
            },
            fail: err => {
              console.error('生成二维码失败:', err);
              resolve('');
            }
          }, this);
        }, 200);
      });
    },
    showQRCode() {
      const chineseText = '这是一段中文内容';
      this.qrCodeUrl = this.generateQRCode(chineseText);
    },

  },
}
</script>

<style scoped lang="scss">
@import 'appDown.scss';
</style>
